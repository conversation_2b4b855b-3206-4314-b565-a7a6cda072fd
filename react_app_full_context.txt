--- FILE: README.md ---
# Getting Started with Create React App

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in your browser.

The page will reload when you make changes.\
You may also see any lint errors in the console.

### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can't go back!**

If you aren't satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you're on your own.

You don't have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn't feel obligated to use this feature. However we understand that this tool wouldn't be useful if you couldn't customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).

### Code Splitting

This section has moved here: [https://facebook.github.io/create-react-app/docs/code-splitting](https://facebook.github.io/create-react-app/docs/code-splitting)

### Analyzing the Bundle Size

This section has moved here: [https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size](https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size)

### Making a Progressive Web App

This section has moved here: [https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app](https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app)

### Advanced Configuration

This section has moved here: [https://facebook.github.io/create-react-app/docs/advanced-configuration](https://facebook.github.io/create-react-app/docs/advanced-configuration)

### Deployment

This section has moved here: [https://facebook.github.io/create-react-app/docs/deployment](https://facebook.github.io/create-react-app/docs/deployment)

### `npm run build` fails to minify

This section has moved here: [https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify](https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify)

--- END FILE: README.md ---

--- FILE: public/index.html ---
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>React App</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>

--- END FILE: public/index.html ---

--- FILE: public/manifest.json ---
{
  "short_name": "React App",
  "name": "Create React App Sample",
  "icons": [
    {
      "src": "favicon.ico",
      "sizes": "64x64 32x32 24x24 16x16",
      "type": "image/x-icon"
    },
    {
      "src": "logo192.png",
      "type": "image/png",
      "sizes": "192x192"
    },
    {
      "src": "logo512.png",
      "type": "image/png",
      "sizes": "512x512"
    }
  ],
  "start_url": ".",
  "display": "standalone",
  "theme_color": "#000000",
  "background_color": "#ffffff"
}

--- END FILE: public/manifest.json ---

--- FILE: public/robots.txt ---
# https://www.robotstxt.org/robotstxt.html
User-agent: *
Disallow:

--- END FILE: public/robots.txt ---

--- FILE: public/custom-script.js ---
// public/custom-script.js
console.log('Custom script loaded at', new Date().toLocaleTimeString());
// …any other global JS you need…


--- END FILE: public/custom-script.js ---

--- FILE: public/goals.md ---
# DATA

## PostDoc_UCLouvain
- ProjectColor: red

| Done | Task                                | Start      | Finish     | Percent |
| ---- | ----------------------------------- | ---------- | ---------- | ------- |
| [x]  | Read papers of latent space project | 2025-02-27 | 2025-04-08 | 100     |
| [ ]  | Website portfolio (work 1 hour)     | 2025-07-01 | 2025-07-15 | 0       |
| [x]  | Think about how to integrate MXenes | 2025-04-01 | 2025-05-14 | 100     |
| [ ]  | Agentic Chatbot (work 5 hours)      | 2025-07-01 | 2025-07-14 | 0       |
| [x]  | PAPER MODNET - recalc for matbench  | 2025-03-14 | 2025-05-20 | 100     |
| [x]  | MODNet improvements organize        | 2025-03-01 | 2025-03-20 | 100     |
| [ ]  | Prepare slides for MXenes proj      | 2025-06-06 | 2025-06-09 | 0       |
| [ ]  | - Write introduction section        |            |            | 0       |
| [ ]  | - Prepare results section           |            |            | 0       |
| [ ]  | - Review and edit                   |            |            | 0       |
| [ ]  | Work on paper for MatterVial        | 2025-06-04 | 2025-06-07 | 0       |
| [x]  | - Discuss the table in MV paper in details 1/3                        |            |            | 0      |
| [ ]  | - Discuss the table in MV paper in details 2/3                        |            |            | 0      |
| [ ]  | - Discuss the table in MV paper in details 3/3                        |            |            | 0      |
| [ ]  | - Make the SHAP plot figure 1/3                                        |            |            | 0      |
| [ ]  | - Make the SHAP plot figure 2/3                                        |            |            | 0      |
| [ ]  | - Make the SHAP plot figure 3/3                                        |            |            | 0      |
| [ ]  | - Interpretability discussion SHAP/SISSO 1/3                           |            |            | 0      |
| [ ]  | - Interpretability discussion SHAP/SISSO 2/3                           |            |            | 0      |
| [ ]  | - Interpretability discussion SHAP/SISSO 3/3                           |            |            | 0      |

## PosDoc_UFRGS_pendencies
- ProjectColor: blue

| Done | Task                                      | Start      | Finish     | Percent |
| ---- | ----------------------------------------- | ---------- | ---------- | ------- |
| [x]  | Read papers of Michele, adjust discussion | 2025-02-26 | 2025-03-02 | 100     |
| [x]  | Organize particle measurements            | 2025-03-01 | 2025-03-07 | 100     |
| [ ]  | Pending calculations second paper         | 2025-04-01 | 2025-05-14 | 0       |
| [ ]  | Pending project DFT Guilherme UFPel       | 2025-04-01 | 2025-05-14 | 0       |
| [x]  | publishing AdvParticleAnalyzer            | 2025-03-06 | 2025-03-07 | 100     |
| [ ]  | paper Michele, work for a few hours       | 2025-05-30 | 2025-06-03 | 0       |

## Pessoais
- ProjectColor: salmon

| Done | Task                            | Start      | Finish     | Percent |
| ---- | ------------------------------- | ---------- | ---------- | ------- |
| [x]  | comprar sabao em pó, cafe decaf | 2025-03-01 | 2025-03-14 | 100     |
| [ ]  | ver uma bike em algum momento   | 2025-04-01 | 2025-06-01 | 0       |
| [ ]  | ver o médico e remedios         | 2025-05-30 | 2025-08-01 | 0       |
| [ ]  |                                 |            |            |         |

## Programming Portfolio
- ProjectColor: orange

| Done | Task                     | Start      | Finish     | Percent |
| ---- | ------------------------ | ---------- | ---------- | ------- |
| [ ]  | Curso tensorflow 3h      | 2024-11-25 | 2024-11-30 | 0       |
| [x]  | Portfolio 3h (cancelled) | 2024-10-02 | 2024-10-09 | 0       |
| [x]  | FScourseSUSPENDED        | 2023-11-22 | 2024-01-31 | 50      |
| [x]  | MyWebsite-SUSPENDED      | 2023-11-30 | 2024-01-31 | 0       |
| [ ]  | Portfolio 3h | 2024-11-25 | 2024-11-30 | 0       |

--- END FILE: public/goals.md ---

--- FILE: public/daily-notes/2025-05-31.md ---
# Daily Note for 2025-05-31

- ✅ Finish React demo  
- 📚 Read GPT-4 spec  zzzz

--- END FILE: public/daily-notes/2025-05-31.md ---

--- FILE: src/App.js ---
// App.js
import React, { useEffect, useState, useRef } from 'react';
import HeaderTimeline from './components/HeaderTimeline';
import SummaryGraph from './components/SummaryGraph';
import FolderSelection from './components/FolderSelection';
import GoalsSection from './components/GoalsSection';
import DailyNotesSection from './components/DailyNotesSection';
import GoalsEditor from './components/GoalsEditor';

const supportsDirectoryPicker = 'showDirectoryPicker' in window;
const DEFAULT_FOLDER_NAME = 'C:\\Users\\<USER>\\OneDrive\\Obsidian\\01-JOURNAL\\01-DAILY';
const GOALS_FILE_NAME = 'goals.md';

function App() {
  // ── State ───────────────────────────────────────────────────────────────
  const [goalsMd, setGoalsMd] = useState('');
  const [dailyMd, setDailyMd] = useState('Please select a folder with your daily notes.');
  const [dailyNoteNames, setDailyNoteNames] = useState([]);
  const [selectedNote, setSelectedNote] = useState(null);
  const [isEditingNote, setIsEditingNote] = useState(false);
  const [isEditingGoals, setIsEditingGoals] = useState(false);
  const [isFolderSelected, setIsFolderSelected] = useState(false);
  const todayISO = new Date().toISOString().slice(0, 10);

  // Folder/File handling
  const [folderHandle, setFolderHandle] = useState(null);
  const fileHandlesRef = useRef({});
  const fileInputRef = useRef(null);
  const goalsFileHandleRef = useRef(null);

  // ── Folder Selection & File Loading ───────────────────────────────────────
  const handleSelectFolder = async () => {
    if (supportsDirectoryPicker) {
      try {
        const handle = await window.showDirectoryPicker();
        await setupSelectedFolder(handle);
      } catch (err) {
        console.error('Directory picker failed or was cancelled', err);
      }
    } else {
      fileInputRef.current.click();
    }
  };

  const setupSelectedFolder = async (handle) => {
    setFolderHandle(handle);
    setIsFolderSelected(true);
    
    try {
      const goalsFileHandle = await handle.getFileHandle(GOALS_FILE_NAME, { create: true });
      goalsFileHandleRef.current = goalsFileHandle;
      
      const goalsFile = await goalsFileHandle.getFile();
      const goalsContent = await goalsFile.text();
      setGoalsMd(goalsContent);
    } catch (goalsError) {
      console.error('Error loading goals file:', goalsError);
      setGoalsMd(''); 
    }

    await loadDailyNoteFiles(handle);
  };

  const loadDailyNoteFiles = async (handle) => {
    const names = [];
    const handles = {};
    for await (const entry of handle.values()) {
      if (entry.kind === 'file' && entry.name.endsWith('.md') && entry.name !== GOALS_FILE_NAME) {
        const noteName = entry.name.replace(/\.md$/i, '');
        names.push(noteName);
        handles[noteName] = entry;
      }
    }
    setDailyNoteNames(names);
    fileHandlesRef.current = handles;
    if (names.length) {
      const sorted = [...names].sort((a, b) => new Date(a) - new Date(b));
      setSelectedNote(sorted[sorted.length - 1]);
    } else {
      setDailyMd('No Markdown files found in this folder.');
    }
  };

  const handleFallbackFiles = (e) => {
    const files = Array.from(e.target.files);
    const names = [];
    const handles = {};
    files.forEach((file) => {
      if (file.name.endsWith('.md') && file.name !== GOALS_FILE_NAME) {
        const noteName = file.name.replace(/\.md$/i, '');
        names.push(noteName);
        handles[noteName] = file;
      }
    });
    setDailyNoteNames(names);
    fileHandlesRef.current = handles;
    setIsFolderSelected(true);
    if (names.length) {
      const sorted = [...names].sort((a, b) => new Date(a) - new Date(b));
      setSelectedNote(sorted[sorted.length - 1]);
    } else {
      setDailyMd('No Markdown files found in this folder.');
    }
  };

  // ── Goals Saving Method ─────────────────────────────────────────────────
  const saveGoalsFile = async (content) => {
    try {
      if (!goalsFileHandleRef.current) {
        throw new Error('No goals file handle available');
      }

      const writable = await goalsFileHandleRef.current.createWritable();
      await writable.write(content);
      await writable.close();

      setGoalsMd(content);
      setIsEditingGoals(false);
    } catch (error) {
      console.error('Error saving goals:', error);
      alert('Failed to save goals. Please try again.');
    }
  };

  // ── Note Content Loading ────────────────────────────────────────────────
  const loadNoteContent = async (noteName) => {
    const handleOrFile = fileHandlesRef.current[noteName];
    if (!handleOrFile) return;
    try {
      let file;
      if ('getFile' in handleOrFile) {
        file = await handleOrFile.getFile();
      } else {
        file = handleOrFile;
      }
      const text = await file.text();
      setDailyMd(text);
    } catch (err) {
      console.error('Error reading note:', err);
      setDailyMd('Error reading note.');
    }
  };

  useEffect(() => {
    if (selectedNote) loadNoteContent(selectedNote);
  }, [selectedNote]);

  // ── Note Handlers ─────────────────────────────────────────────────────────
  const handleNoteSelect = (noteName) => {
    setSelectedNote(noteName);
    setIsEditingNote(false);
    loadNoteContent(noteName);
  };

  const handleCreateNote = () => {
    setSelectedNote(todayISO);
    setDailyMd('');
    setIsEditingNote(true);
    if (!dailyNoteNames.includes(todayISO)) {
      setDailyNoteNames((prev) => [...prev, todayISO]);
    }
  };

  const handleSaveNote = async (content) => {
    const handleOrFile = fileHandlesRef.current[selectedNote];

    if (handleOrFile && 'createWritable' in handleOrFile) {
      try {
        const writable = await handleOrFile.createWritable();
        await writable.write(content);
        await writable.close();
        setDailyMd(content);
        setIsEditingNote(false);
      } catch (err) {
        console.error('Error saving note:', err);
      }
    } else {
      const newFile = new Blob([content], { type: 'text/markdown' });
      const fileUrl = URL.createObjectURL(newFile);
      const a = document.createElement('a');
      a.href = fileUrl;
      a.download = `${selectedNote}.md`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(fileUrl);
    }
  };

  const handleEditNote = () => setIsEditingNote(true);

  // ── Goals Loading ─────────────────────────────────────────────────────────
  useEffect(() => {
    fetch('/goals.md')
      .then((r) => {
        if (!r.ok) throw new Error('Failed to load goals.md');
        return r.text();
      })
      .then(setGoalsMd)
      .catch((err) => console.error(err));
  }, []);

  return (
    <div
      style={{
        maxWidth: 1000,
        margin: '2rem auto',
        fontFamily: 'system-ui, -apple-system, sans-serif',
      }}
    >
      <header>
        <h1>My Daily Notes App</h1>
      </header>
      <HeaderTimeline />
      <main>
        {/* 1) Always show Folder Selection if no folder selected */}
        {!isFolderSelected && (
          <FolderSelection
            supportsDirectoryPicker={supportsDirectoryPicker}
            DEFAULT_FOLDER_NAME={DEFAULT_FOLDER_NAME}
            handleSelectFolder={handleSelectFolder}
            fileInputRef={fileInputRef}
            handleFallbackFiles={handleFallbackFiles}
          />
        )}

        {/* Rest of the app is conditionally rendered only after folder selection */}
        {isFolderSelected && (
          <>
            {/* Goals Section */}
            {isEditingGoals ? (
              <GoalsEditor 
                initialContent={goalsMd}
                onSave={saveGoalsFile}
                onCancel={() => setIsEditingGoals(false)}
              />
            ) : (
              <GoalsSection
                goalsMd={goalsMd}
                onEditGoals={() => setIsEditingGoals(true)}
              />
            )}

            {/* Weekly summary graph */}
            {dailyNoteNames.length > 0 && (
              <SummaryGraph 
                dailyNoteNames={dailyNoteNames} 
                fileHandlesRef={fileHandlesRef} 
              />
            )}

            {/* Daily Notes Section */}
            {dailyNoteNames.length > 0 && (
              <DailyNotesSection
                dailyMd={dailyMd}
                dailyNoteNames={dailyNoteNames}
                selectedNote={selectedNote}
                isEditingNote={isEditingNote}
                onSelectNote={handleNoteSelect}
                onCreateNote={handleCreateNote}
                onEditNote={handleEditNote}
                onSaveNote={handleSaveNote}
              />
            )}
          </>
        )}

        <input
          type="file"
          ref={fileInputRef}
          style={{ display: 'none' }}
          webkitdirectory="true"
          directory="true"
          multiple
          onChange={handleFallbackFiles}
        />
      </main>
    </div>
  );
}

export default App;

--- END FILE: src/App.js ---

--- FILE: src/App.css ---
.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

--- END FILE: src/App.css ---

--- FILE: src/App.test.js ---
import { render, screen } from '@testing-library/react';
import App from './App';

test('renders learn react link', () => {
  render(<App />);
  const linkElement = screen.getByText(/learn react/i);
  expect(linkElement).toBeInTheDocument();
});

--- END FILE: src/App.test.js ---

--- FILE: src/components/DailyNoteEditor.js ---
import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const DailyNoteEditor = ({ initialContent, onSave, onCancel }) => {
  const [content, setContent] = useState(initialContent);
  const [showLivePreview, setShowLivePreview] = useState(true);

  const handleTogglePreview = () => {
    setShowLivePreview(prev => !prev);
  };

  return (
    <div
      style={{
        border: '1px solid #ddd',
        borderRadius: '4px',
        padding: '0.5rem',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
      }}
    >
      <div style={{ marginBottom: '0.5rem', textAlign: 'right' }}>
        <button
          onClick={handleTogglePreview}
          style={{
            padding: '0.25rem 0.5rem',
            backgroundColor: '#78909C',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          {showLivePreview ? 'Hide Live Preview' : 'Show Live Preview'}
        </button>
      </div>
      {showLivePreview ? (
        <div style={{ maxWidth: 800, display: 'flex', gap: '1rem' }}>
          {/* Preview panel comes first */}
          <div
            style={{
              width: '50%',
              minHeight: '300px',
              padding: '0.5rem',
              border: '1px solid #ddd',
              borderRadius: '4px',
              overflowY: 'auto',
              background: '#fafafa',
              wordWrap: 'break-word',       // Enable line breaks
              overflowWrap: 'break-word',   // Support modern browsers
            }}
          >
            <ReactMarkdown remarkPlugins={[remarkGfm]}>
              {content}
            </ReactMarkdown>
          </div>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            style={{
              width: '50%',
              minHeight: '300px',
              padding: '0.5rem',
              border: '1px solid #ddd',
              borderRadius: '4px',
              fontFamily: 'monospace',
              resize: 'vertical',
              wordWrap: 'break-word',       // Enable line breaks
              overflowWrap: 'break-word',   // Support modern browsers
            }}
          />
        </div>
      ) : (
        <textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          style={{
            width: '100%',
            minHeight: '300px',
            padding: '0.5rem',
            border: '1px solid #ddd',
            borderRadius: '4px',
            fontFamily: 'monospace',
            resize: 'vertical',
            wordWrap: 'break-word',       // Enable line breaks
            overflowWrap: 'break-word',   // Support modern browsers
          }}
        />
      )}
      <div
        style={{
          display: 'flex',
          justifyContent: 'flex-end',
          marginTop: '0.5rem',
          gap: '0.5rem',
        }}
      >
        <button
          onClick={onCancel}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: '#f44336',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          Cancel
        </button>
        <button
          onClick={() => onSave(content)}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: '#4CAF50',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          Save
        </button>
      </div>
    </div>
  );
};

export default DailyNoteEditor;

--- END FILE: src/components/DailyNoteEditor.js ---

--- FILE: src/components/DailyNotesList.js ---
// DailyNotesList.js
import React from 'react';

const DailyNotesList = ({ notes, selectedNote, onSelectNote }) => {
  // newest-first
  const sortedNotes = [...notes].sort((a, b) => b.localeCompare(a));

  return (
    <div
      style={{
        width: '200px',
        flexShrink: 0,            // never shrink below 200px
        border: '1px solid #ddd',
        borderRadius: '4px',
        padding: '0.5rem',
        maxHeight: '70vh',        // so it fits in viewport
        overflowY: 'auto',
      }}
    >
      <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1rem' }}>
        Daily Notes
      </h3>
      <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
        {sortedNotes.length === 0 ? (
          <li style={{ padding: '0.5rem', color: '#666' }}>
            No notes found
          </li>
        ) : (
          sortedNotes.map((note) => (
            <li
              key={note}
              onClick={() => onSelectNote(note)}
              style={{
                padding: '0.5rem',
                cursor: 'pointer',
                backgroundColor:
                  note === selectedNote ? '#f0f0f0' : 'transparent',
                borderRadius: '4px',
                marginBottom: '0.25rem',
                transition: 'background-color 0.2s',
              }}
            >
              {note === new Date().toISOString().slice(0, 10) ? (
                <strong>{note} (Today)</strong>
              ) : (
                note
              )}
            </li>
          ))
        )}
      </ul>
    </div>
  );
};

export default DailyNotesList;

--- END FILE: src/components/DailyNotesList.js ---

--- FILE: src/components/DailyNotesSection.js ---
// components/DailyNotesSection.js
import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import DailyNotesList from './DailyNotesList';
import DailyNoteEditor from './DailyNoteEditor';

const DailyNotesSection = ({
  dailyMd,
  dailyNoteNames,
  selectedNote,
  isEditingNote,
  onSelectNote,
  onCreateNote,
  onEditNote,
  onSaveNote,
}) => {
  return (
    <section style={{ marginBottom: '2rem' }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1rem',
        }}
      >
        <h2>Note ({selectedNote})</h2>
        <div>
          <button
            onClick={onCreateNote}
            style={{
              marginRight: 8,
              padding: '0.5rem 1rem',
              backgroundColor: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: 4,
              cursor: 'pointer',
            }}
          >
            New Today's Note
          </button>
          {!isEditingNote && (
            <button
              onClick={onEditNote}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: '#2196F3',
                color: 'white',
                border: 'none',
                borderRadius: 4,
                cursor: 'pointer',
              }}
            >
              Edit Note
            </button>
          )}
        </div>
      </div>
      <div style={{ maxWidth: 400, display: 'flex', gap: 16 }}>
        <DailyNotesList
          notes={dailyNoteNames}
          selectedNote={selectedNote}
          onSelectNote={onSelectNote}
        />
        <div style={{ flex: 1 }}>
          {isEditingNote ? (
            <DailyNoteEditor initialContent={dailyMd} onSave={onSaveNote} onCancel={() => {}} />
          ) : (
            <article
              style={{
                maxWidth: 400,
                border: '1px solid #ddd',
                padding: 16,
                borderRadius: 4,
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                minHeight: 300,
              }}
            >
              <ReactMarkdown remarkPlugins={[remarkGfm]}>{dailyMd}</ReactMarkdown>
            </article>
          )}
        </div>
      </div>
    </section>
  );
};

export default DailyNotesSection;

--- END FILE: src/components/DailyNotesSection.js ---

--- FILE: src/components/FolderSelection.js ---
// components/FolderSelection.js
import React from 'react';

const FolderSelection = ({
  supportsDirectoryPicker,
  DEFAULT_FOLDER_NAME,
  handleSelectFolder,
  fileInputRef,
  handleFallbackFiles,
}) => {
  return (
    <section style={{ marginBottom: '2rem' }}>
      <p>
        Welcome! It seems like you haven't selected your daily notes folder yet.
        {supportsDirectoryPicker ? (
          <>
            Please select the <b>{DEFAULT_FOLDER_NAME}</b> folder, or any other folder
            containing your notes.
          </>
        ) : (
          <>
            Your browser doesn't support the native folder picker. Please choose your daily notes folder
            using the button below.
          </>
        )}
      </p>
      <button
        onClick={handleSelectFolder}
        style={{
          padding: '0.5rem 1rem',
          backgroundColor: '#2196F3',
          color: 'white',
          border: 'none',
          borderRadius: 4,
          cursor: 'pointer',
        }}
      >
        Select Daily Notes Folder
      </button>
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        webkitdirectory="true"
        directory="true"
        multiple
        onChange={handleFallbackFiles}
      />
    </section>
  );
};

export default FolderSelection;

--- END FILE: src/components/FolderSelection.js ---

--- FILE: src/components/GoalsEditor.js ---
// components/GoalsEditor.js
import React, { useState, useEffect } from 'react';

const GoalsEditor = ({ initialContent, onSave, onCancel }) => {
  const [projects, setProjects] = useState([]);
  const [editingProject, setEditingProject] = useState(null);
  const [editingTask, setEditingTask] = useState(null);

  // Parse initial content when component mounts
  useEffect(() => {
    parseGoalsContent(initialContent);
  }, [initialContent]);

  const parseGoalsContent = (content) => {
    const lines = content.split('\n');
    const parsedProjects = [];
    let currentProject = null;

    lines.forEach(line => {
      if (line.startsWith('## ')) {
        // New project
        if (currentProject) {
          parsedProjects.push(currentProject);
        }
        currentProject = {
          name: line.slice(3).trim(),
          color: '',
          tasks: []
        };
      } else if (line.startsWith('- ProjectColor:') && currentProject) {
        currentProject.color = line.split(':')[1].trim();
      } else if (line.startsWith('| ') && line.includes('| Task |')) {
        // Skip header rows
        return;
      } else if (line.startsWith('| ')) {
        // Task row
        const columns = line.split('|').map(col => col.trim());
        if (columns.length >= 6 && currentProject) {
          currentProject.tasks.push({
            done: columns[1],
            task: columns[2],
            start: columns[3] || '',
            finish: columns[4] || '',
            percent: columns[5] || '0'
          });
        }
      }
    });

    // Add last project
    if (currentProject) {
      parsedProjects.push(currentProject);
    }

    setProjects(parsedProjects);
  };

  const generateMarkdownContent = () => {
    return projects.map(project => {
      let projectContent = `## ${project.name}\n`;
      projectContent += `- ProjectColor: ${project.color}\n\n`;
      projectContent += `| Done | Task | Start | Finish | Percent |\n`;
      projectContent += `| ---- | ---- | ----- | ------ | ------- |\n`;
      
      project.tasks.map(task => {
        projectContent += `| ${task.done} | ${task.task} | ${task.start} | ${task.finish} | ${task.percent} |\n`;
      });
      
      return projectContent;
    }).join('\n');
  };

  const handleSave = () => {
    const markdownContent = generateMarkdownContent();
    onSave(markdownContent);
  };

  const addProject = () => {
    setProjects([
      ...projects, 
      { 
        name: 'New Project', 
        color: 'gray', 
        tasks: [] 
      }
    ]);
  };

  const addTask = (projectIndex) => {
    const newProjects = [...projects];
    newProjects[projectIndex].tasks.push({
      done: '[ ]',
      task: 'New Task',
      start: '',
      finish: '',
      percent: '0'
    });
    setProjects(newProjects);
  };

  const updateProject = (projectIndex, field, value) => {
    const newProjects = [...projects];
    newProjects[projectIndex][field] = value;
    setProjects(newProjects);
  };

  const updateTask = (projectIndex, taskIndex, field, value) => {
    const newProjects = [...projects];
    newProjects[projectIndex].tasks[taskIndex][field] = value;
    setProjects(newProjects);
  };

  const deleteProject = (projectIndex) => {
    const newProjects = projects.filter((_, index) => index !== projectIndex);
    setProjects(newProjects);
  };

  const deleteTask = (projectIndex, taskIndex) => {
    const newProjects = [...projects];
    newProjects[projectIndex].tasks = newProjects[projectIndex].tasks.filter((_, index) => index !== taskIndex);
    setProjects(newProjects);
  };

  return (
    <div style={{ 
      maxWidth: '1000px', 
      margin: 'auto', 
      padding: '20px', 
      backgroundColor: '#f5f5f5',
      borderRadius: '8px'
    }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        marginBottom: '20px' 
      }}>
        <h2>Goals Editor</h2>
        <div>
          <button 
            onClick={addProject}
            style={{
              marginRight: '10px',
              padding: '5px 10px',
              backgroundColor: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '4px'
            }}
          >
            Add Project
          </button>
          <button 
            onClick={handleSave}
            style={{
              marginRight: '10px',
              padding: '5px 10px',
              backgroundColor: '#2196F3',
              color: 'white',
              border: 'none',
              borderRadius: '4px'
            }}
          >
            Save
          </button>
          <button 
            onClick={onCancel}
            style={{
              padding: '5px 10px',
              backgroundColor: '#f44336',
              color: 'white',
              border: 'none',
              borderRadius: '4px'
            }}
          >
            Cancel
          </button>
        </div>
      </div>

      {projects.map((project, projectIndex) => (
        <div 
          key={projectIndex} 
          style={{ 
            backgroundColor: 'white', 
            marginBottom: '20px', 
            padding: '15px', 
            borderRadius: '8px',
            boxShadow: '0 2px 5px rgba(0,0,0,0.1)'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
            <input 
              value={project.name}
              onChange={(e) => updateProject(projectIndex, 'name', e.target.value)}
              style={{ 
                flex: 1, 
                marginRight: '10px', 
                padding: '5px',
                fontSize: '1.2em',
                fontWeight: 'bold'
              }}
            />
            <input 
              value={project.color}
              onChange={(e) => updateProject(projectIndex, 'color', e.target.value)}
              placeholder="Project Color"
              style={{ 
                width: '100px', 
                padding: '5px',
                marginRight: '10px'
              }}
            />
            <button 
              onClick={() => deleteProject(projectIndex)}
              style={{
                backgroundColor: '#f44336',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                padding: '5px 10px'
              }}
            >
              Delete Project
            </button>
          </div>

          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ backgroundColor: '#f1f1f1' }}>
                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Done</th>
                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Task</th>
                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Start</th>
                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Finish</th>
                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Percent</th>
                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {project.tasks.map((task, taskIndex) => (
                <tr key={taskIndex}>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                    <select 
                      value={task.done}
                      onChange={(e) => updateTask(projectIndex, taskIndex, 'done', e.target.value)}
                    >
                      <option value="[ ]">Incomplete</option>
                      <option value="[x]">Complete</option>
                    </select>
                  </td>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                    <input 
                      value={task.task}
                      onChange={(e) => updateTask(projectIndex, taskIndex, 'task', e.target.value)}
                      style={{ width: '100%' }}
                    />
                  </td>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                    <input 
                      type="date"
                      value={task.start}
                      onChange={(e) => updateTask(projectIndex, taskIndex, 'start', e.target.value)}
                    />
                  </td>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                    <input 
                      type="date"
                      value={task.finish}
                      onChange={(e) => updateTask(projectIndex, taskIndex, 'finish', e.target.value)}
                    />
                  </td>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                    <input 
                      type="number"
                      min="0"
                      max="100"
                      value={task.percent}
                      onChange={(e) => updateTask(projectIndex, taskIndex, 'percent', e.target.value)}
                      style={{ width: '60px' }}
                    />
                  </td>
                  <td style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'center' }}>
                    <button 
                      onClick={() => deleteTask(projectIndex, taskIndex)}
                      style={{
                        backgroundColor: '#f44336',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        padding: '3px 6px'
                      }}
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          <button 
            onClick={() => addTask(projectIndex)}
            style={{
              marginTop: '10px',
              backgroundColor: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              padding: '5px 10px'
            }}
          >
            Add Task
          </button>
        </div>
      ))}
    </div>
  );
};

export default GoalsEditor;

--- END FILE: src/components/GoalsEditor.js ---

--- FILE: src/components/GoalsSection.js ---
// components/GoalsSection.js
import React, { useEffect, useRef } from 'react';
import Plotly from 'plotly.js-dist-min';
import GoalsEditor from './GoalsEditor';

const GoalsSection = ({ goalsMd, isEditingGoals, onEditGoals, onSaveGoals }) => {
  const fullPlotRef = useRef(null);
  const weekPlotRef = useRef(null);
  const fullContainerRef = useRef(null);
  const weekContainerRef = useRef(null);
  const todayISO = new Date().toISOString().slice(0, 10);

  // Effect to generate Plotly charts from the goals markdown
  useEffect(() => {
    if (!goalsMd || isEditingGoals) return;

    // --- Extract the DATA section from goalsMd ---
    const dataMatch = /# DATA([\s\S]*?)(?:# NOTES|$)/.exec(goalsMd);
    if (!dataMatch) return;
    const dataSection = dataMatch[1].trim();

    // Parse table helper function (used to extract tasks)
    function parseTable(table) {
      const rows = table
        .split('\n')
        .filter(Boolean)
        .map((r) => r.split('|').map((c) => c.trim()));
      const headers = rows[0].filter(Boolean);
      const items = rows.slice(2).map((row) => {
        const obj = {};
        headers.forEach((h, i) => (obj[h] = row[i + 1] || ''));
        if (obj.Task.startsWith('-')) {
          obj.isSubtask = true;
          obj.Task = obj.Task.slice(1).trim();
        } else {
          obj.isSubtask = false;
        }
        if (obj.Done === '[x]') {
          obj.State = 'Complete';
        } else if (obj.Done === '[ ]') {
          obj.State = 'Incomplete';
        }
        return obj;
      });

      const out = [];
      let current = null;
      for (let i = 0; i < items.length; i++) {
        if (!items[i].isSubtask) {
          current = items[i];
          out.push(current);
          const subtasks = [];
          let j = i + 1;
          while (j < items.length && items[j].isSubtask) {
            items[j].parentTask = current.Task;
            subtasks.push(items[j]);
            j++;
          }
          if (subtasks.length) {
            const startDate = new Date(current.Start);
            const finishDate = new Date(current.Finish);
            const msPerDay = 86400000;
            const totalDays = Math.ceil((finishDate - startDate) / msPerDay);
            const totalSubtasks = subtasks.length;
            let taskIndex = 0;
            for (let i = 0; i < totalSubtasks; i++) {
              const dayIndex = i / (totalSubtasks / totalDays);
              const dayStart = new Date(startDate.getTime() + dayIndex * msPerDay);
              let dayFinish;
              if (dayIndex < totalDays - 1) {
                dayFinish = new Date(dayStart.getTime() + msPerDay + 1);
              } else {
                dayFinish = new Date(finishDate.getTime());
              }
              const st = subtasks[taskIndex];
              st.Start = dayStart.toISOString().slice(0, 10);
              st.Finish = dayFinish.toISOString().slice(0, 10);
              st.Percent = st.Percent || '0';
              st.State = st.State || 'Incomplete';
              taskIndex++;
              if (taskIndex >= totalSubtasks) {
                break;
              }
            }
          }
          out.push(...subtasks);
          i = j - 1;
        }
      }
      return out;
    }

    // Parse projects from the DATA section
    function parseProjects(text) {
      const lines = text.split('\n');
      const projects = [];
      let project = null;
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line.startsWith('## ')) {
          project = { ProjectName: line.slice(3), Tasks: [] };
        } else if (line.startsWith('- ProjectColor:') && project) {
          project.ProjectColor = line.split(':')[1].trim();
        } else if (line.startsWith('|') && project) {
          const block = [];
          while (i < lines.length && lines[i].startsWith('|')) {
            block.push(lines[i]);
            i++;
          }
          project.Tasks = parseTable(block.join('\n'));
          projects.push(project);
          project = null;
          i--;
        }
      }
      return projects;
    }

    // Generate time marks for Plotly
    function generateTimeMarks(year) {
      const months = [
        'JANUARY',
        'FEBRUARY',
        'MARCH',
        'APRIL',
        'MAY',
        'JUNE',
        'JULY',
        'AUGUST',
        'SEPTEMBER',
        'OCTOBER',
        'NOVEMBER',
        'DECEMBER',
      ];
      const curMonth = new Date().getMonth();
      const nextMonth = (curMonth + 1) % 12;
      const marks = [curMonth, nextMonth].map((m) => {
        const start = `${year}-${String(m + 1).padStart(2, '0')}-01`;
        const dayCount = new Date(year, m + 1, 0).getDate();
        const finish = `${year}-${String(m + 1).padStart(2, '0')}-${dayCount}`;
        return {
          Task: months[m],
          Start: start,
          Finish: finish,
          Percent: '100',
          State: 'Complete',
        };
      });
      return [{ ProjectName: 'TIMEMARKS', ProjectColor: 'blue', Tasks: marks }];
    }

    const userProjects = parseProjects(dataSection);
    const allStarts = userProjects.flatMap(p => p.Tasks.map(t => t.Start)).filter(Boolean);
    const years = allStarts.map(s => +s.slice(0, 4)).sort();
    const refYear = years[0] || new Date().getFullYear();
    const twoMonthsAgo = new Date();
    twoMonthsAgo.setMonth(twoMonthsAgo.getMonth() - 1);

    const entries = [
      ...generateTimeMarks(refYear),
      ...userProjects,
    ]
      .map(proj => ({
        ...proj,
        Tasks: proj.Tasks.filter(t => {
          const finishDate = new Date(t.Finish);
          return !isNaN(finishDate.getTime()) && finishDate > twoMonthsAgo;
        }),
      }))
      .filter(p => p.Tasks.length);

    // Plotly data arrays and layout settings
    const data = [];
    const shapes = [];
    const fullA = [];
    const weekA = [];
    const yTicks = [];
    let counter = 0;
    const weekStart = new Date();
    weekStart.setHours(0, 0, 0, 0);
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 7);
    const wsISO = weekStart.toISOString().slice(0, 10);
    const weISO = weekEnd.toISOString().slice(0, 10);

    function dateAtPercent(s, f, p) {
      const sd = new Date(s),
        fd = new Date(f);
      if (isNaN(sd.getTime()) || isNaN(fd.getTime())) return null;
      const endDate = new Date(sd.getTime() + (fd - sd) * (p / 100));
      return endDate.toISOString().slice(0, 10);
    }

    function addRect(x0, x1, c, y0, y1, o = 0.5) {
      if (!x0 || !x1 || isNaN(new Date(x0).getTime()) || isNaN(new Date(x1).getTime())) {
        console.warn('Skipping shape due to invalid dates:', x0, x1);
        return;
      }
      shapes.push({
        type: 'rect',
        x0,
        x1,
        y0,
        y1,
        xref: 'x',
        yref: 'y',
        fillcolor: c,
        opacity: o,
        line: { width: 0 },
      });
    }

    entries.forEach(proj => {
      yTicks.push(proj.ProjectName);
      const subs = proj.Tasks.filter(t => t.isSubtask);
      const mains = proj.Tasks.filter(t => !t.isSubtask);
      const byParent = {};
      subs.forEach(s => {
        byParent[s.parentTask] = byParent[s.parentTask] || [];
        byParent[s.parentTask].push(s);
      });
      const sorted = [];
      mains.forEach(m => {
        sorted.push(m);
        if (byParent[m.Task]) sorted.push(...byParent[m.Task]);
      });

      let inner = 0;
      sorted.forEach(task => {
        const sub = task.isSubtask;
        const yPos = counter + inner * 0.2;
        const shapeColor = task.State === 'Complete' ? 'black' : proj.ProjectColor;
        const shapeOpacity = 1;
        addRect(
          task.Start,
          task.Finish,
          shapeColor,
          yPos - (sub ? 0.03 : 0.05),
          yPos + (sub ? 0.03 : 0.05),
          shapeOpacity
        );

        if (task.State !== 'Not Started') {
          const end =
            task.State === 'Incomplete'
              ? dateAtPercent(task.Start, task.Finish, +task.Percent)
              : task.Finish;
          const endDate = new Date(end);
          if (end && !isNaN(endDate.getTime())) {
            addRect(
              task.Start,
              end,
              'black',
              yPos - (sub ? 0.03 : 0.07),
              yPos + (sub ? 0.03 : 0.07),
              1
            );
          }
        }

        data.push({
          type: 'scatter',
          mode: 'lines',
          x: [task.Start, task.Finish],
          y: [yPos, yPos],
          hoverinfo: 'text',
          text: `${task.Task}<br>${task.Start} → ${task.Finish}<br>${task.State}`,
          line: { width: 8, color: 'rgba(0,0,0,0)' },
        });

        const finishDate = new Date(task.Finish);
        if (!isNaN(finishDate.getTime())) {
          fullA.push({
            x: new Date(finishDate.getTime()),
            y: yPos,
            xref: 'x',
            yref: 'y',
            text: (sub ? '↳ ' : '') + task.Task,
            showarrow: false,
            font: {
              family: 'Arial Black, sans-serif',
              size: sub ? 10 : 12,
              color: '#000',
            },
            align: 'left',
          });
        } else {
          console.warn(`Skipping full annotation for task "${task.Task}" due to invalid finish date: ${task.Finish}`);
        }

        const ts = new Date(task.Start),
          tf = new Date(task.Finish);
        if (!isNaN(ts.getTime()) && !isNaN(tf.getTime()) && tf >= weekStart && ts <= weekEnd) {
          const vs = ts < weekStart ? weekStart : ts;
          const ve = tf > weekEnd ? weekEnd : tf;
          const midpoint = new Date((vs.getTime() + ve.getTime()) / 2);
          if (!isNaN(midpoint.getTime())) {
            weekA.push({
              x: ve,
              y: yPos,
              xref: 'x',
              yref: 'y',
              text: (sub ? '↳ ' : '') + task.Task,
              showarrow: false,
              font: {
                family: 'Arial Black, sans-serif',
                size: sub ? 10 : 12,
                color: '#000',
              },
              align: 'center',
              borderwidth: 1,
              borderpad: 2,
            });
          } else {
            console.warn(
              `Skipping weekly annotation for task "${task.Task}" due to invalid midpoint calculation for dates: ${vs.toISOString().slice(
                0,
                10
              )} to ${ve.toISOString().slice(0, 10)}`
            );
          }
        }
        inner++;
      });

      counter += inner * 0.2 + 0.2;
      for (let i = 0; i < inner - 1; i++) yTicks.push('');
      if (sorted.length > 0) yTicks.push('');
    });

    const finalTickVals = yTicks.map((_, i) => i * 0.2);
    shapes.push({
      type: 'line',
      x0: todayISO,
      x1: todayISO,
      y0: 0,
      y1: counter,
      line: { color: 'darkred', width: 2, dash: 'dot' },
    });

    const fullLayout = {
      autosize: true,
      margin: { l: 150, r: 150, t: 30, b: 50 },
      xaxis: {
        type: 'date',
        rangeselector: {
          buttons: [
            { count: 7, step: 'day', stepmode: 'backward', label: '1w' },
            { count: 1, step: 'month', stepmode: 'backward', label: '1m' },
            { step: 'all', label: 'All' },
          ],
        },
      },
      yaxis: {
        ticktext: yTicks,
        tickvals: finalTickVals,
        autorange: 'reversed',
      },
      shapes,
      annotations: fullA,
      showlegend: false,
    };

    const weekLayout = {
      autosize: true,
      margin: { l: 150, r: 30, t: 30, b: 50 },
      xaxis: { type: 'date', autorange: false, range: [wsISO, weISO] },
      yaxis: {
        ticktext: yTicks,
        tickvals: finalTickVals,
        autorange: 'reversed',
      },
      shapes,
      annotations: weekA,
      showlegend: false,
    };

    Plotly.react(fullPlotRef.current, data, fullLayout, { responsive: true });
    Plotly.react(weekPlotRef.current, data, weekLayout, { responsive: true });
  }, [goalsMd, isEditingGoals, todayISO]);

  // Resize observer for responsive charts
  useEffect(() => {
    if (isEditingGoals) return;
    if (!fullContainerRef.current || !weekContainerRef.current) return;

    const roFull = new ResizeObserver(() => {
      Plotly.Plots.resize(fullPlotRef.current);
    });
    const roWeek = new ResizeObserver(() => {
      Plotly.Plots.resize(weekPlotRef.current);
    });

    roFull.observe(fullContainerRef.current);
    roWeek.observe(weekContainerRef.current);

    return () => {
      roFull.disconnect();
      roWeek.disconnect();
    };
  }, [isEditingGoals]);

  return (
    <section style={{ marginBottom: '2rem' }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1rem',
        }}
      >
        <h2>Goals Timeline</h2>
        {!isEditingGoals && (
          <button
            onClick={onEditGoals}
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#2196F3',
              color: 'white',
              border: 'none',
              borderRadius: 4,
              cursor: 'pointer',
            }}
          >
            Edit Goals
          </button>
        )}
      </div>
      {isEditingGoals ? (
        <GoalsEditor
          initialContent={goalsMd}
          onSave={onSaveGoals}
          onCancel={() => onSaveGoals(goalsMd)}
        />
      ) : (
        <>
          <div style={{ marginBottom: '2rem' }}>
            <h3>Long term</h3>
            <div
              ref={fullContainerRef}
              style={{
                resize: 'both',
                overflow: 'auto',
                width: '100%',
                height: 400,
                border: '1px solid #eee',
                borderRadius: 4,
              }}
            >
              <div ref={fullPlotRef} style={{ width: '100%', height: '100%' }} />
            </div>
          </div>
          <div>
            <h3>This week</h3>
            <div
              ref={weekContainerRef}
              style={{
                resize: 'both',
                overflow: 'auto',
                width: '100%',
                height: 400,
                border: '1px solid #eee',
                borderRadius: 4,
              }}
            >
              <div ref={weekPlotRef} style={{ width: '100%', height: '100%' }} />
            </div>
          </div>
        </>
      )}
    </section>
  );
};

export default GoalsSection;

--- END FILE: src/components/GoalsSection.js ---

--- FILE: src/components/HeaderTimeline.js ---
import React from 'react';

const monthNames = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

// same colour map you had in your dataviewjs
const monthColors = [
  '#ccffcc','#66ff66','#00ff00','#009900',
  '#ffcccc','#ff6666','#ff0000','#990000',
  '#ccccff','#6666ff','#0000ff','#000099'
];

const HeaderTimeline = () => {
  // Use the current date
  const today = new Date();
  const year = today.getFullYear();
  const monthIndex = today.getMonth(); // 0-based
  const dayOfMonth = today.getDate();
  const dateISO = today.toISOString().slice(0,10);
  const dayOfWeek = today.toLocaleDateString(undefined, { weekday: 'long' });

  // layout constants
  const scale = 10;     // px per day
  const gap = 10;       // px between months
  const barHeight = 25; // height of each month‐bar
  const svgHeight = 100;

  // compute days in each month for this year (handles leap)
  const monthDays = Array.from({ length: 12 }, (_, i) =>
    new Date(year, i + 1, 0).getDate()
  );

  // prefix‐sum x positions for each month’s bar
  const positions = monthDays.reduce((acc, days, i) => {
    if (i === 0) return [0];
    const prev = acc[i - 1] + monthDays[i - 1] * scale + gap;
    return [...acc, prev];
  }, []);

  // total width of the svg
  const svgWidth = positions[11] + monthDays[11] * scale;

  // circle positioning: center of the current day’s “column”
  const circleX = positions[monthIndex] + (dayOfMonth - 1) * scale + scale / 2;
  const circleY = 14;    // roughly barHeight/2 + offset
  const circleR = 15;

  // styling for the date text
  const colour = monthColors[monthIndex];
  const shadow = 'black';
  const dateStyle = {
    color: colour,
    textShadow: `1px 1px 1px ${shadow}, -1px -1px 1px ${shadow}`
  };

  return (
    <header style={{ marginBottom: '2rem' }}>
      {/* Weekday + Date */}
      <div style={{
        display: 'flex',
        alignItems: 'baseline',
        gap: '0.5rem',
        fontSize: '1.5rem'
      }}>
        <span style={{ color: '#111111' }}>{dayOfWeek},</span>
        <span style={dateStyle}>{dateISO}</span>
      </div>

      {/* Timeline SVG */}
      <svg
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        style={{ width: '100%', height: 'auto', marginTop: '1rem' }}
      >
        {/* month bars */}
        <g className="bars">
          {monthDays.map((days, i) => (
            <rect
              key={i}
              x={positions[i]}
              y={0}
              width={days * scale}
              height={barHeight}
              fill={monthColors[i]}
            />
          ))}
        </g>

        {/* month labels */}
        <g
          className="labels"
          style={{
            fontSize: '3.2rem',
            fill: '#747474',
            textAnchor: 'start'
          }}
        >
          {monthNames.map((name, i) => (
            <text
              key={i}
              x={positions[i]}
              y={80}      /* hard‐coded to match your original */
            >
              {name}
            </text>
          ))}
        </g>

        {/* current‐day circle */}
        <circle
          cx={circleX}
          cy={circleY}
          r={circleR}
          fill="white"
          stroke="black"
        />
      </svg>
    </header>
  );
};

export default HeaderTimeline;

--- END FILE: src/components/HeaderTimeline.js ---

--- FILE: src/components/SummaryGraph.js ---
import React, { useEffect, useRef, useState } from 'react'
import Plotly from 'plotly.js-dist-min'

const customColors = [
  '#1f77b4','#ff7f0e','#2ca02c','#d62728','#9467bd',
  '#8c564b','#e377c2','#7f7f7f','#bcbd22','#17becf',
  '#7cb5ec','#434348','#90ed7d','#f7a35c','#8085e9',
  '#f15c80','#e4d354','#2b908f','#f45b5b','#91e8e1'
]

export default function SummaryGraph({ dailyNoteNames, fileHandlesRef }) {
  const weeklyRef  = useRef(null)
  const monthlyRef = useRef(null)
  const colorDict  = useRef({})

  const [weeklyHeight, setWeeklyHeight]   = useState(300)
  const [monthlyHeight, setMonthlyHeight] = useState(400)

  // helper to zero‐pad
  function pad(n) {
    return n < 10 ? '0' + n : '' + n
  }

  // yyyy-mm-dd in local time
  function isoDate(d) {
    return `${d.getFullYear()}-${pad(d.getMonth()+1)}-${pad(d.getDate())}`
  }

  // yyyy-mm in local time
  function isoMonth(d) {
    return `${d.getFullYear()}-${pad(d.getMonth()+1)}`
  }

  useEffect(() => {
    if (!dailyNoteNames?.length) return
    console.log('Loaded dailyNoteNames:', dailyNoteNames)

    // ── parse a single day's note ───────────────────────────────────────
    async function parseNote(dateStr) {
      console.log(`\n→ parseNote("${dateStr}") start`)
      const handleOrFile = fileHandlesRef.current[dateStr]
      let txt = ''
      try {
        if (handleOrFile?.getFile) {
          txt = await (await handleOrFile.getFile()).text()
        } else if (handleOrFile?.text) {
          txt = await handleOrFile.text()
        }
      } catch (e) {
        console.warn('failed to load', dateStr, e)
      }

      // only inside # Events … # Relax & Reflect
      const sec  = /# Events([\s\S]*?)(?:# Relax & Reflect|$)/.exec(txt)
      const body = sec ? sec[1] : txt

      const timedRe  = /-\s*(?!\[)(.*?)\s*#([^\s\[]+)\s*\[startTime::\s*(\d{2}:\d{2})\]\s*\[endTime::\s*(\d{2}:\d{2})\]/g
      const simpleRe = /-(?!\s*\[)(.*?)\s*#([^\s\[]+)\s+(\d+)(?!.*::)/g
      const doneRe   = /-\s*\[x\]\s*(.*?)\s*#([^\s\[]+)\s+(\d+)/gi

      const byType = {}
      let m

      // timed
      while ((m = timedRe.exec(body))) {
        console.log('timedRe match:', m)
        const [, description, type, start, end] = m
        const diff = calculateTimeDifference(start, end)
        if (!byType[type]) byType[type] = { time: 0, points: 0 }
        byType[type].time   += diff
        byType[type].points += diff
      }

      // simple time
      while ((m = simpleRe.exec(body))) {
        console.log('simpleRe match:', m)
        const [, description, type, minsStr] = m
        const mins = +minsStr
        if (!byType[type]) byType[type] = { time: 0, points: 0 }
        byType[type].time   += mins
        byType[type].points += mins
      }

      // completed-only points
      while ((m = doneRe.exec(body))) {
        console.log('doneRe match:', m)
        const [, description, type, ptsStr] = m
        const pts = +ptsStr
        if (!byType[type]) byType[type] = { time: 0, points: 0 }
        byType[type].points += pts
      }

      console.log(`← parseNote("${dateStr}") result:`, byType)
      return byType
    }


    function calculateTimeDifference(start, end) {
      const [h1,m1] = start.split(':').map(Number)
      const [h2,m2] = end.split(':').map(Number)
      return (h2 * 60 + m2) - (h1 * 60 + m1)
    }

    // ── Weekly ────────────────────────────────────────────────────────────
    async function buildWeekly() {
      console.log('\n=== buildWeekly ===')
      const today = new Date()
      const days  = [...Array(7)].map((_, i) => {
        const d = new Date(today)
        d.setDate(today.getDate() - (6 - i))
        return isoDate(d)
      })
      console.log('Weekly days:', days)

      const traces = {}
      for (let d of days) {
        const byType = await parseNote(d)
        for (let [type, data] of Object.entries(byType)) {
          if (!traces[type]) {
            const col = colorDict.current[type] ||
                        customColors[Object.keys(colorDict.current).length % customColors.length]
            traces[type] = { x:[], y:[], type:'bar', name:type, marker:{color:col} }
            colorDict.current[type] = col
          }
          traces[type].x.push(`${d} - Time`, `${d} - Points`)
          traces[type].y.push(data.time, data.points)
        }
      }
      console.log('Weekly traces:', traces)

      Plotly.react(
        weeklyRef.current,
        Object.values(traces),
        {
          title:  'Weekly Summary',
          barmode: 'stack',
          xaxis:  { title:'Date' },
          yaxis:  { title:'Value (mins/pts)' },
          legend: { orientation:'h', y:-0.2 },
          margin: { t:40, b:80 }
        },
        { responsive: true }
      )
    }

    // ── Monthly with correct isoMonth() ──────────────────────────────────
    async function buildMonthly() {
      console.log('\n=== buildMonthly ===')
      const today = new Date()

      // 1) calendar months: two ago, one ago, this
      const months = [2,1,0].map(offset => {
        const m = new Date(today.getFullYear(), today.getMonth() - offset, 1)
        return {
          iso:   isoMonth(m),  // local "YYYY-MM"
          label: m.toLocaleString('default',{ month:'short', year:'numeric' })
        }
      })
      console.log('Months buckets:', months)

      // 2) aggregator: { type: [pts_2mo, pts_1mo, pts_0mo] }
      const agg = {}

      // 3) fill each bucket
      for (let i = 0; i < months.length; i++) {
        const { iso, label } = months[i]
        const list = dailyNoteNames.filter(name => name.startsWith(iso))
        console.log(`→ Bucket "${label}" [${iso}]: matched files →`, list)

        for (let dateStr of list) {
          console.log(`   • parsing ${dateStr} for ${label}`)
          const byType = await parseNote(dateStr)
          for (let [type, data] of Object.entries(byType)) {
            if (!agg[type]) agg[type] = [0,0,0]
            const before = agg[type][i]
            agg[type][i] += data.points
            console.log(`     - ${type}: +${data.points} pts (was ${before}, now ${agg[type][i]})`)
          }
        }
      }

      console.log('Final monthly agg:', agg)

      // 4) make traces
      const traces = Object.entries(agg).map(([type, arr]) => {
        if (!colorDict.current[type]) {
          const used = Object.keys(colorDict.current).length
          colorDict.current[type] = customColors[used % customColors.length]
        }
        const t = {
          x:     months.map(m => m.label),
          y:     arr,
          name:  type,
          type:  'bar',
          marker:{ color: colorDict.current[type] }
        }
        console.log('Monthly trace for', type, '→', t)
        return t
      })

      // 5) render
      Plotly.react(
        monthlyRef.current,
        traces,
        {
          title:  'Monthly Productivity (last 3 months)',
          barmode:'stack',
          xaxis:  { title:'Month' },
          yaxis:  { title:'Points' },
          legend: { orientation:'h', y:-0.2 },
          margin: { t:40, b:80 }
        },
        { responsive: true }
      )
    }

    buildWeekly()
    buildMonthly()
  }, [dailyNoteNames, fileHandlesRef])

  const cardStyle = {
    border: '1px solid #eee',
    borderRadius: 4,
    background: '#fff',
    padding: '1rem',
    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
    marginBottom: '2rem'
  }

  const handleWeeklyResize  = e => setWeeklyHeight(e.target.offsetHeight)
  const handleMonthlyResize = e => setMonthlyHeight(e.target.offsetHeight)

  return (
    <section>
      <div style={cardStyle}>
        <h2>Weekly Summary</h2>
        <div
          style={{ width:'100%', height:weeklyHeight, resize:'both', overflow:'auto' }}
          onMouseDown={handleWeeklyResize}
        >
          <div ref={weeklyRef} style={{ width:'100%', height:'100%' }}/>
        </div>
      </div>

      <div style={cardStyle}>
        <h2>Monthly Productivity (Last 3 Months)</h2>
        <div
          style={{ width:'100%', height:monthlyHeight, resize:'both', overflow:'auto' }}
          onMouseDown={handleMonthlyResize}
        >
          <div ref={monthlyRef} style={{ width:'100%', height:'100%' }}/>
        </div>
      </div>
    </section>
  )
}

--- END FILE: src/components/SummaryGraph.js ---

--- FILE: main.js ---
// main.js
const { app, BrowserWindow } = require('electron')
const path = require('path')

function createWindow() {
  const win = new BrowserWindow({
    width: 1000,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true
    }
  })
  // Load the CRA build
  win.loadFile(path.join(__dirname, 'build', 'index.html'))
}

app.whenReady().then(createWindow)

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit()
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) createWindow()
})


--- END FILE: main.js ---

