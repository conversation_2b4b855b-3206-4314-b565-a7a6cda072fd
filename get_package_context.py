import os

def concatenate_project_files(root_dir, output_file=None):
    """
    Concatenates the content of specified project files into a single string,
    prefixed with their relative paths.

    Args:
        root_dir (str): The root directory of your project.
        output_file (str, optional): If provided, the concatenated content
                                     will be written to this file.
                                     Otherwise, it will be printed to stdout.
    Returns:
        str: The concatenated content.
    """
    # Define the files and directories to include from your React app.
    # I've tried to include common and important files based on your provided structure.
    # You can customize this list further!
    files_to_include = [
        'README.md',
        # 'package.json',
        # 'package-lock.json',
        # Public directory files
        'public/index.html',
        'public/manifest.json',
        'public/robots.txt',
        'public/custom-script.js',
        'public/goals.md',
        # Example of including a specific daily note, you might want to iterate or generalize this
        'public/daily-notes/2025-05-31.md',
        # Source directory files
        'src/App.js',
        'src/App.css',
        'src/App.test.js',
        # Components
        'src/components/DailyNoteEditor.js',
        'src/components/DailyNotesList.js',
        'src/components/DailyNotesSection.js',
        'src/components/FolderSelection.js',
        'src/components/GoalsEditor.js',
        'src/components/GoalsSection.js',
        'src/components/HeaderTimeline.js',
        'src/components/SummaryGraph.js',
        # You might also want to include main.js if it's a custom entry point,
        # but typically Create React App uses src/index.js (which wasn't in your list,
        # so I'm assuming main.js is your entry or build output).
        'main.js', # If this is your main application logic or a build output you want to include
    ]

    concatenated_content = []

    for file_path in files_to_include:
        full_path = os.path.join(root_dir, file_path)
        if os.path.exists(full_path):
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                concatenated_content.append(f"--- FILE: {file_path} ---\n")
                concatenated_content.append(content)
                concatenated_content.append(f"\n--- END FILE: {file_path} ---\n\n")
            except Exception as e:
                concatenated_content.append(f"--- ERROR READING FILE: {file_path} - {e} ---\n\n")
        else:
            concatenated_content.append(f"--- FILE NOT FOUND: {file_path} ---\n\n")

    final_string = "".join(concatenated_content)

    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(final_string)
        print(f"Concatenated content written to {output_file}")
    else:
        print(final_string)

    return final_string

if __name__ == "__main__":
    # Get the current working directory as the root
    # Make sure you run this script from the root directory of your React project
    project_root = os.getcwd()

    # Option 1: Print to console (default)
    # concatenate_project_files(project_root)

    # Option 2: Write to a file (recommended for LLM input)
    concatenate_project_files(project_root, output_file="react_app_full_context.txt")

    print("\nScript finished. Check the console or 'react_app_full_context.txt'.")
