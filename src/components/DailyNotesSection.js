// components/DailyNotesSection.js
import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import DailyNotesList from './DailyNotesList';
import DailyNoteEditor from './DailyNoteEditor';

const DailyNotesSection = ({
  dailyMd,
  dailyNoteNames,
  selectedNote,
  isEditingNote,
  onSelectNote,
  onCreateNote,
  onEditNote,
  onSaveNote,
}) => {
  return (
    <section style={{ marginBottom: '2rem' }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1rem',
        }}
      >
        <h2>Note ({selectedNote})</h2>
        <div>
          <button
            onClick={onCreateNote}
            style={{
              marginRight: 8,
              padding: '0.5rem 1rem',
              backgroundColor: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: 4,
              cursor: 'pointer',
            }}
          >
            New Today's Note
          </button>
          {!isEditingNote && (
            <button
              onClick={onEditNote}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: '#2196F3',
                color: 'white',
                border: 'none',
                borderRadius: 4,
                cursor: 'pointer',
              }}
            >
              Edit Note
            </button>
          )}
        </div>
      </div>
      <div style={{ maxWidth: 400, display: 'flex', gap: 16 }}>
        <DailyNotesList
          notes={dailyNoteNames}
          selectedNote={selectedNote}
          onSelectNote={onSelectNote}
        />
        <div style={{ flex: 1 }}>
          {isEditingNote ? (
            <DailyNoteEditor initialContent={dailyMd} onSave={onSaveNote} onCancel={() => {}} />
          ) : (
            <article
              style={{
                maxWidth: 400,
                border: '1px solid #ddd',
                padding: 16,
                borderRadius: 4,
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                minHeight: 300,
              }}
            >
              <ReactMarkdown remarkPlugins={[remarkGfm]}>{dailyMd}</ReactMarkdown>
            </article>
          )}
        </div>
      </div>
    </section>
  );
};

export default DailyNotesSection;
