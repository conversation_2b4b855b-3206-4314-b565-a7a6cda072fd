import React, { useEffect, useRef, useState } from 'react'
import Plotly from 'plotly.js-dist-min'

const customColors = [
  '#1f77b4','#ff7f0e','#2ca02c','#d62728','#9467bd',
  '#8c564b','#e377c2','#7f7f7f','#bcbd22','#17becf',
  '#7cb5ec','#434348','#90ed7d','#f7a35c','#8085e9',
  '#f15c80','#e4d354','#2b908f','#f45b5b','#91e8e1'
]

export default function SummaryGraph({ dailyNoteNames, fileHandlesRef }) {
  const weeklyRef  = useRef(null)
  const monthlyRef = useRef(null)
  const colorDict  = useRef({})

  const [weeklyHeight, setWeeklyHeight]   = useState(300)
  const [monthlyHeight, setMonthlyHeight] = useState(400)

  // helper to zero‐pad
  function pad(n) {
    return n < 10 ? '0' + n : '' + n
  }

  // yyyy-mm-dd in local time
  function isoDate(d) {
    return `${d.getFullYear()}-${pad(d.getMonth()+1)}-${pad(d.getDate())}`
  }

  // yyyy-mm in local time
  function isoMonth(d) {
    return `${d.getFullYear()}-${pad(d.getMonth()+1)}`
  }

  useEffect(() => {
    if (!dailyNoteNames?.length) return
    console.log('Loaded dailyNoteNames:', dailyNoteNames)

    // ── parse a single day's note ───────────────────────────────────────
    async function parseNote(dateStr) {
      console.log(`\n→ parseNote("${dateStr}") start`)
      const handleOrFile = fileHandlesRef.current[dateStr]
      let txt = ''
      try {
        if (handleOrFile?.getFile) {
          txt = await (await handleOrFile.getFile()).text()
        } else if (handleOrFile?.text) {
          txt = await handleOrFile.text()
        }
      } catch (e) {
        console.warn('failed to load', dateStr, e)
      }

      // only inside # Events … # Relax & Reflect
      const sec  = /# Events([\s\S]*?)(?:# Relax & Reflect|$)/.exec(txt)
      const body = sec ? sec[1] : txt

      const timedRe  = /-\s*(?!\[)(.*?)\s*#([^\s\[]+)\s*\[startTime::\s*(\d{2}:\d{2})\]\s*\[endTime::\s*(\d{2}:\d{2})\]/g
      const simpleRe = /-(?!\s*\[)(.*?)\s*#([^\s\[]+)\s+(\d+)(?!.*::)/g
      const doneRe   = /-\s*\[x\]\s*(.*?)\s*#([^\s\[]+)\s+(\d+)/gi

      const byType = {}
      let m

      // timed
      while ((m = timedRe.exec(body))) {
        console.log('timedRe match:', m)
        const [, description, type, start, end] = m
        const diff = calculateTimeDifference(start, end)
        if (!byType[type]) byType[type] = { time: 0, points: 0 }
        byType[type].time   += diff
        byType[type].points += diff
      }

      // simple time
      while ((m = simpleRe.exec(body))) {
        console.log('simpleRe match:', m)
        const [, description, type, minsStr] = m
        const mins = +minsStr
        if (!byType[type]) byType[type] = { time: 0, points: 0 }
        byType[type].time   += mins
        byType[type].points += mins
      }

      // completed-only points
      while ((m = doneRe.exec(body))) {
        console.log('doneRe match:', m)
        const [, description, type, ptsStr] = m
        const pts = +ptsStr
        if (!byType[type]) byType[type] = { time: 0, points: 0 }
        byType[type].points += pts
      }

      console.log(`← parseNote("${dateStr}") result:`, byType)
      return byType
    }


    function calculateTimeDifference(start, end) {
      const [h1,m1] = start.split(':').map(Number)
      const [h2,m2] = end.split(':').map(Number)
      return (h2 * 60 + m2) - (h1 * 60 + m1)
    }

    // ── Weekly ────────────────────────────────────────────────────────────
    async function buildWeekly() {
      console.log('\n=== buildWeekly ===')
      const today = new Date()
      const days  = [...Array(7)].map((_, i) => {
        const d = new Date(today)
        d.setDate(today.getDate() - (6 - i))
        return isoDate(d)
      })
      console.log('Weekly days:', days)

      const traces = {}
      for (let d of days) {
        const byType = await parseNote(d)
        for (let [type, data] of Object.entries(byType)) {
          if (!traces[type]) {
            const col = colorDict.current[type] ||
                        customColors[Object.keys(colorDict.current).length % customColors.length]
            traces[type] = { x:[], y:[], type:'bar', name:type, marker:{color:col} }
            colorDict.current[type] = col
          }
          traces[type].x.push(`${d} - Time`, `${d} - Points`)
          traces[type].y.push(data.time, data.points)
        }
      }
      console.log('Weekly traces:', traces)

      Plotly.react(
        weeklyRef.current,
        Object.values(traces),
        {
          title:  'Weekly Summary',
          barmode: 'stack',
          xaxis:  { title:'Date' },
          yaxis:  { title:'Value (mins/pts)' },
          legend: { orientation:'h', y:-0.2 },
          margin: { t:40, b:80 }
        },
        { responsive: true }
      )
    }

    // ── Monthly with correct isoMonth() ──────────────────────────────────
    async function buildMonthly() {
      console.log('\n=== buildMonthly ===')
      const today = new Date()

      // 1) calendar months: two ago, one ago, this
      const months = [2,1,0].map(offset => {
        const m = new Date(today.getFullYear(), today.getMonth() - offset, 1)
        return {
          iso:   isoMonth(m),  // local "YYYY-MM"
          label: m.toLocaleString('default',{ month:'short', year:'numeric' })
        }
      })
      console.log('Months buckets:', months)

      // 2) aggregator: { type: [pts_2mo, pts_1mo, pts_0mo] }
      const agg = {}

      // 3) fill each bucket
      for (let i = 0; i < months.length; i++) {
        const { iso, label } = months[i]
        const list = dailyNoteNames.filter(name => name.startsWith(iso))
        console.log(`→ Bucket "${label}" [${iso}]: matched files →`, list)

        for (let dateStr of list) {
          console.log(`   • parsing ${dateStr} for ${label}`)
          const byType = await parseNote(dateStr)
          for (let [type, data] of Object.entries(byType)) {
            if (!agg[type]) agg[type] = [0,0,0]
            const before = agg[type][i]
            agg[type][i] += data.points
            console.log(`     - ${type}: +${data.points} pts (was ${before}, now ${agg[type][i]})`)
          }
        }
      }

      console.log('Final monthly agg:', agg)

      // 4) make traces
      const traces = Object.entries(agg).map(([type, arr]) => {
        if (!colorDict.current[type]) {
          const used = Object.keys(colorDict.current).length
          colorDict.current[type] = customColors[used % customColors.length]
        }
        const t = {
          x:     months.map(m => m.label),
          y:     arr,
          name:  type,
          type:  'bar',
          marker:{ color: colorDict.current[type] }
        }
        console.log('Monthly trace for', type, '→', t)
        return t
      })

      // 5) render
      Plotly.react(
        monthlyRef.current,
        traces,
        {
          title:  'Monthly Productivity (last 3 months)',
          barmode:'stack',
          xaxis:  { title:'Month' },
          yaxis:  { title:'Points' },
          legend: { orientation:'h', y:-0.2 },
          margin: { t:40, b:80 }
        },
        { responsive: true }
      )
    }

    buildWeekly()
    buildMonthly()
  }, [dailyNoteNames, fileHandlesRef])

  const cardStyle = {
    border: '1px solid #eee',
    borderRadius: 4,
    background: '#fff',
    padding: '1rem',
    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
    marginBottom: '2rem'
  }

  const handleWeeklyResize  = e => setWeeklyHeight(e.target.offsetHeight)
  const handleMonthlyResize = e => setMonthlyHeight(e.target.offsetHeight)

  return (
    <section>
      <div style={cardStyle}>
        <h2>Weekly Summary</h2>
        <div
          style={{ width:'100%', height:weeklyHeight, resize:'both', overflow:'auto' }}
          onMouseDown={handleWeeklyResize}
        >
          <div ref={weeklyRef} style={{ width:'100%', height:'100%' }}/>
        </div>
      </div>

      <div style={cardStyle}>
        <h2>Monthly Productivity (Last 3 Months)</h2>
        <div
          style={{ width:'100%', height:monthlyHeight, resize:'both', overflow:'auto' }}
          onMouseDown={handleMonthlyResize}
        >
          <div ref={monthlyRef} style={{ width:'100%', height:'100%' }}/>
        </div>
      </div>
    </section>
  )
}
