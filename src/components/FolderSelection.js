// components/FolderSelection.js
import React from 'react';

const FolderSelection = ({
  supportsDirectoryPicker,
  DEFAULT_FOLDER_NAME,
  handleSelectFolder,
  fileInputRef,
  handleFallbackFiles,
}) => {
  return (
    <section style={{ marginBottom: '2rem' }}>
      <p>
        Welcome! It seems like you haven't selected your daily notes folder yet.
        {supportsDirectoryPicker ? (
          <>
            Please select the <b>{DEFAULT_FOLDER_NAME}</b> folder, or any other folder
            containing your notes.
          </>
        ) : (
          <>
            Your browser doesn't support the native folder picker. Please choose your daily notes folder
            using the button below.
          </>
        )}
      </p>
      <button
        onClick={handleSelectFolder}
        style={{
          padding: '0.5rem 1rem',
          backgroundColor: '#2196F3',
          color: 'white',
          border: 'none',
          borderRadius: 4,
          cursor: 'pointer',
        }}
      >
        Select Daily Notes Folder
      </button>
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        webkitdirectory="true"
        directory="true"
        multiple
        onChange={handleFallbackFiles}
      />
    </section>
  );
};

export default FolderSelection;
