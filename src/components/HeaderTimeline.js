import React from 'react';

const monthNames = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

// same colour map you had in your dataviewjs
const monthColors = [
  '#ccffcc','#66ff66','#00ff00','#009900',
  '#ffcccc','#ff6666','#ff0000','#990000',
  '#ccccff','#6666ff','#0000ff','#000099'
];

const HeaderTimeline = () => {
  // Use the current date
  const today = new Date();
  const year = today.getFullYear();
  const monthIndex = today.getMonth(); // 0-based
  const dayOfMonth = today.getDate();
  const dateISO = today.toISOString().slice(0,10);
  const dayOfWeek = today.toLocaleDateString(undefined, { weekday: 'long' });

  // layout constants
  const scale = 10;     // px per day
  const gap = 10;       // px between months
  const barHeight = 25; // height of each month‐bar
  const svgHeight = 100;

  // compute days in each month for this year (handles leap)
  const monthDays = Array.from({ length: 12 }, (_, i) =>
    new Date(year, i + 1, 0).getDate()
  );

  // prefix‐sum x positions for each month’s bar
  const positions = monthDays.reduce((acc, days, i) => {
    if (i === 0) return [0];
    const prev = acc[i - 1] + monthDays[i - 1] * scale + gap;
    return [...acc, prev];
  }, []);

  // total width of the svg
  const svgWidth = positions[11] + monthDays[11] * scale;

  // circle positioning: center of the current day’s “column”
  const circleX = positions[monthIndex] + (dayOfMonth - 1) * scale + scale / 2;
  const circleY = 14;    // roughly barHeight/2 + offset
  const circleR = 15;

  // styling for the date text
  const colour = monthColors[monthIndex];
  const shadow = 'black';
  const dateStyle = {
    color: colour,
    textShadow: `1px 1px 1px ${shadow}, -1px -1px 1px ${shadow}`
  };

  return (
    <header style={{ marginBottom: '2rem' }}>
      {/* Weekday + Date */}
      <div style={{
        display: 'flex',
        alignItems: 'baseline',
        gap: '0.5rem',
        fontSize: '1.5rem'
      }}>
        <span style={{ color: '#111111' }}>{dayOfWeek},</span>
        <span style={dateStyle}>{dateISO}</span>
      </div>

      {/* Timeline SVG */}
      <svg
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        style={{ width: '100%', height: 'auto', marginTop: '1rem' }}
      >
        {/* month bars */}
        <g className="bars">
          {monthDays.map((days, i) => (
            <rect
              key={i}
              x={positions[i]}
              y={0}
              width={days * scale}
              height={barHeight}
              fill={monthColors[i]}
            />
          ))}
        </g>

        {/* month labels */}
        <g
          className="labels"
          style={{
            fontSize: '3.2rem',
            fill: '#747474',
            textAnchor: 'start'
          }}
        >
          {monthNames.map((name, i) => (
            <text
              key={i}
              x={positions[i]}
              y={80}      /* hard‐coded to match your original */
            >
              {name}
            </text>
          ))}
        </g>

        {/* current‐day circle */}
        <circle
          cx={circleX}
          cy={circleY}
          r={circleR}
          fill="white"
          stroke="black"
        />
      </svg>
    </header>
  );
};

export default HeaderTimeline;
