// App.js
import React, { useEffect, useState, useRef } from 'react';
import HeaderTimeline from './components/HeaderTimeline';
import SummaryGraph from './components/SummaryGraph';
import FolderSelection from './components/FolderSelection';
import GoalsSection from './components/GoalsSection';
import DailyNotesSection from './components/DailyNotesSection';
import GoalsEditor from './components/GoalsEditor';

const supportsDirectoryPicker = 'showDirectoryPicker' in window;
const DEFAULT_FOLDER_NAME = 'C:\\Users\\<USER>\\OneDrive\\Obsidian\\01-JOURNAL\\01-DAILY';
const GOALS_FILE_NAME = 'goals.md';

function App() {
  // ── State ───────────────────────────────────────────────────────────────
  const [goalsMd, setGoalsMd] = useState('');
  const [dailyMd, setDailyMd] = useState('Please select a folder with your daily notes.');
  const [dailyNoteNames, setDailyNoteNames] = useState([]);
  const [selectedNote, setSelectedNote] = useState(null);
  const [isEditingNote, setIsEditingNote] = useState(false);
  const [isEditingGoals, setIsEditingGoals] = useState(false);
  const [isFolderSelected, setIsFolderSelected] = useState(false);
  const todayISO = new Date().toISOString().slice(0, 10);

  // Folder/File handling
  const [folderHandle, setFolderHandle] = useState(null);
  const fileHandlesRef = useRef({});
  const fileInputRef = useRef(null);
  const goalsFileHandleRef = useRef(null);

  // ── Folder Selection & File Loading ───────────────────────────────────────
  const handleSelectFolder = async () => {
    if (supportsDirectoryPicker) {
      try {
        const handle = await window.showDirectoryPicker();
        await setupSelectedFolder(handle);
      } catch (err) {
        console.error('Directory picker failed or was cancelled', err);
      }
    } else {
      fileInputRef.current.click();
    }
  };

  const setupSelectedFolder = async (handle) => {
    setFolderHandle(handle);
    setIsFolderSelected(true);
    
    try {
      const goalsFileHandle = await handle.getFileHandle(GOALS_FILE_NAME, { create: true });
      goalsFileHandleRef.current = goalsFileHandle;
      
      const goalsFile = await goalsFileHandle.getFile();
      const goalsContent = await goalsFile.text();
      setGoalsMd(goalsContent);
    } catch (goalsError) {
      console.error('Error loading goals file:', goalsError);
      setGoalsMd(''); 
    }

    await loadDailyNoteFiles(handle);
  };

  const loadDailyNoteFiles = async (handle) => {
    const names = [];
    const handles = {};
    for await (const entry of handle.values()) {
      if (entry.kind === 'file' && entry.name.endsWith('.md') && entry.name !== GOALS_FILE_NAME) {
        const noteName = entry.name.replace(/\.md$/i, '');
        names.push(noteName);
        handles[noteName] = entry;
      }
    }
    setDailyNoteNames(names);
    fileHandlesRef.current = handles;
    if (names.length) {
      const sorted = [...names].sort((a, b) => new Date(a) - new Date(b));
      setSelectedNote(sorted[sorted.length - 1]);
    } else {
      setDailyMd('No Markdown files found in this folder.');
    }
  };

  const handleFallbackFiles = (e) => {
    const files = Array.from(e.target.files);
    const names = [];
    const handles = {};
    files.forEach((file) => {
      if (file.name.endsWith('.md') && file.name !== GOALS_FILE_NAME) {
        const noteName = file.name.replace(/\.md$/i, '');
        names.push(noteName);
        handles[noteName] = file;
      }
    });
    setDailyNoteNames(names);
    fileHandlesRef.current = handles;
    setIsFolderSelected(true);
    if (names.length) {
      const sorted = [...names].sort((a, b) => new Date(a) - new Date(b));
      setSelectedNote(sorted[sorted.length - 1]);
    } else {
      setDailyMd('No Markdown files found in this folder.');
    }
  };

  // ── Goals Saving Method ─────────────────────────────────────────────────
  const saveGoalsFile = async (content) => {
    try {
      if (!goalsFileHandleRef.current) {
        throw new Error('No goals file handle available');
      }

      const writable = await goalsFileHandleRef.current.createWritable();
      await writable.write(content);
      await writable.close();

      setGoalsMd(content);
      setIsEditingGoals(false);
    } catch (error) {
      console.error('Error saving goals:', error);
      alert('Failed to save goals. Please try again.');
    }
  };

  // ── Note Content Loading ────────────────────────────────────────────────
  const loadNoteContent = async (noteName) => {
    const handleOrFile = fileHandlesRef.current[noteName];
    if (!handleOrFile) return;
    try {
      let file;
      if ('getFile' in handleOrFile) {
        file = await handleOrFile.getFile();
      } else {
        file = handleOrFile;
      }
      const text = await file.text();
      setDailyMd(text);
    } catch (err) {
      console.error('Error reading note:', err);
      setDailyMd('Error reading note.');
    }
  };

  useEffect(() => {
    if (selectedNote) loadNoteContent(selectedNote);
  }, [selectedNote]);

  // ── Note Handlers ─────────────────────────────────────────────────────────
  const handleNoteSelect = (noteName) => {
    setSelectedNote(noteName);
    setIsEditingNote(false);
    loadNoteContent(noteName);
  };

  const handleCreateNote = () => {
    setSelectedNote(todayISO);
    setDailyMd('');
    setIsEditingNote(true);
    if (!dailyNoteNames.includes(todayISO)) {
      setDailyNoteNames((prev) => [...prev, todayISO]);
    }
  };

  const handleSaveNote = async (content) => {
    const handleOrFile = fileHandlesRef.current[selectedNote];

    if (handleOrFile && 'createWritable' in handleOrFile) {
      try {
        const writable = await handleOrFile.createWritable();
        await writable.write(content);
        await writable.close();
        setDailyMd(content);
        setIsEditingNote(false);
      } catch (err) {
        console.error('Error saving note:', err);
      }
    } else {
      const newFile = new Blob([content], { type: 'text/markdown' });
      const fileUrl = URL.createObjectURL(newFile);
      const a = document.createElement('a');
      a.href = fileUrl;
      a.download = `${selectedNote}.md`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(fileUrl);
    }
  };

  const handleEditNote = () => setIsEditingNote(true);

  // ── Goals Loading ─────────────────────────────────────────────────────────
  useEffect(() => {
    fetch('/goals.md')
      .then((r) => {
        if (!r.ok) throw new Error('Failed to load goals.md');
        return r.text();
      })
      .then(setGoalsMd)
      .catch((err) => console.error(err));
  }, []);

  return (
    <div
      style={{
        maxWidth: 1000,
        margin: '2rem auto',
        fontFamily: 'system-ui, -apple-system, sans-serif',
      }}
    >
      <header>
        <h1>My Daily Notes App</h1>
      </header>
      <HeaderTimeline />
      <main>
        {/* 1) Always show Folder Selection if no folder selected */}
        {!isFolderSelected && (
          <FolderSelection
            supportsDirectoryPicker={supportsDirectoryPicker}
            DEFAULT_FOLDER_NAME={DEFAULT_FOLDER_NAME}
            handleSelectFolder={handleSelectFolder}
            fileInputRef={fileInputRef}
            handleFallbackFiles={handleFallbackFiles}
          />
        )}

        {/* Rest of the app is conditionally rendered only after folder selection */}
        {isFolderSelected && (
          <>
            {/* Goals Section */}
            {isEditingGoals ? (
              <GoalsEditor 
                initialContent={goalsMd}
                onSave={saveGoalsFile}
                onCancel={() => setIsEditingGoals(false)}
              />
            ) : (
              <GoalsSection
                goalsMd={goalsMd}
                onEditGoals={() => setIsEditingGoals(true)}
              />
            )}

            {/* Weekly summary graph */}
            {dailyNoteNames.length > 0 && (
              <SummaryGraph 
                dailyNoteNames={dailyNoteNames} 
                fileHandlesRef={fileHandlesRef} 
              />
            )}

            {/* Daily Notes Section */}
            {dailyNoteNames.length > 0 && (
              <DailyNotesSection
                dailyMd={dailyMd}
                dailyNoteNames={dailyNoteNames}
                selectedNote={selectedNote}
                isEditingNote={isEditingNote}
                onSelectNote={handleNoteSelect}
                onCreateNote={handleCreateNote}
                onEditNote={handleEditNote}
                onSaveNote={handleSaveNote}
              />
            )}
          </>
        )}

        <input
          type="file"
          ref={fileInputRef}
          style={{ display: 'none' }}
          webkitdirectory="true"
          directory="true"
          multiple
          onChange={handleFallbackFiles}
        />
      </main>
    </div>
  );
}

export default App;
